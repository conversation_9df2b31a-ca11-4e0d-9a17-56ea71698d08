# WPF开发规范文档

## 目录

1. [项目结构规范](#1-项目结构规范)
2. [代码规范](#2-代码规范)
3. [自定义控件开发规范](#3-自定义控件开发规范)
4. [样式和主题规范](#4-样式和主题规范)
5. [行为(Behaviors)开发规范](#5-行为behaviors开发规范)
6. [MVVM架构规范](#6-mvvm架构规范)
7. [性能优化规范](#7-性能优化规范)
8. [测试规范](#8-测试规范)

---

## 1. 项目结构规范

### 1.1 标准解决方案目录结构

```
MyWpfApplication/                    # 解决方案根目录
├── MyWpfApplication.sln             # 解决方案文件
├── src/                             # 源代码目录
│   ├── MyWpfApplication/            # 主应用项目
│   │   ├── Views/                   # 视图文件
│   │   ├── ViewModels/              # 视图模型
│   │   ├── Models/                  # 数据模型
│   │   ├── Services/                # 业务服务
│   │   ├── Controls/                # 自定义控件
│   │   ├── Themes/                  # 主题样式
│   │   └── Assets/                  # 资源文件
│   ├── MyWpfApplication.Core/       # 核心类库
│   └── MyWpfApplication.Data/       # 数据访问层
├── tests/                           # 测试项目
└── docs/                            # 文档目录
```

### 1.2 项目类型分类

| 项目类型 | 用途 | 输出类型 | 命名规范 |
|---------|------|----------|----------|
| 主应用项目 | WPF应用程序入口 | WPF Application | `{ProjectName}` |
| 核心类库 | 业务逻辑和核心功能 | Class Library | `{ProjectName}.Core` |
| 数据访问层 | 数据访问和持久化 | Class Library | `{ProjectName}.Data` |
| 单元测试 | 业务逻辑测试 | Test Project | `{ProjectName}.Tests` |

### 1.3 命名约定

**✅ 应该做：**
```csharp
// 类名使用PascalCase
public class CustomerViewModel { }
public class OrderService { }

// 文件夹使用PascalCase
Views/CustomerManagement/
Models/BusinessEntities/
```

**❌ 不应该做：**
```csharp
// 避免使用下划线或连字符
customer_view_model.cs
order-service.cs
```

---

## 2. 代码规范

### 2.1 C# 代码风格标准

**✅ 推荐的代码格式：**
```csharp
namespace MyWpfApplication.ViewModels
{
    /// <summary>
    /// 客户管理视图模型
    /// </summary>
    public class CustomerViewModel : ViewModelBase
    {
        #region Private Fields
        
        private string _customerName;
        private ObservableCollection<Customer> _customers;
        
        #endregion
        
        #region Public Properties
        
        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }
        
        #endregion
        
        #region Commands
        
        public ICommand SaveCommand { get; }
        
        #endregion
        
        #region Constructor
        
        public CustomerViewModel()
        {
            SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave);
        }
        
        #endregion
        
        #region Private Methods
        
        private void ExecuteSave()
        {
            // 保存逻辑
        }
        
        private bool CanExecuteSave()
        {
            return !string.IsNullOrWhiteSpace(CustomerName);
        }
        
        #endregion
    }
}
```

### 2.2 命名规范

| 元素类型 | 命名规范 | 示例 |
|---------|----------|------|
| 类 | PascalCase | `CustomerService`, `OrderViewModel` |
| 接口 | I + PascalCase | `ICustomerService`, `IRepository<T>` |
| 方法 | PascalCase | `GetCustomers()`, `SaveOrder()` |
| 属性 | PascalCase | `CustomerName`, `IsEnabled` |
| 字段(private) | _camelCase | `_customerName`, `_isLoading` |
| 常量 | PascalCase | `MaxRetryCount`, `DefaultTimeout` |

### 2.3 XAML 代码格式化规则

**✅ 推荐的XAML格式：**
```xml
<UserControl x:Class="MyWpfApplication.Views.CustomerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:MyWpfApplication.ViewModels">
    
    <UserControl.DataContext>
        <vm:CustomerViewModel />
    </UserControl.DataContext>
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0"
                   Text="客户管理"
                   FontSize="24"
                   FontWeight="Bold" />
        
        <DataGrid Grid.Row="1"
                  ItemsSource="{Binding Customers}"
                  AutoGenerateColumns="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="姓名"
                                    Binding="{Binding Name}" />
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
```

---

## 3. 自定义控件开发规范

### 3.1 UserControl vs CustomControl 选择

| 场景 | 推荐类型 | 理由 |
|------|----------|------|
| 组合现有控件 | UserControl | 简单快速，适合业务特定UI |
| 需要完全自定义外观 | CustomControl | 支持主题，可重用性强 |
| 简单的数据展示 | UserControl | 开发成本低 |
| 复杂交互逻辑 | CustomControl | 更好的封装和扩展性 |

### 3.2 依赖属性定义模板

```csharp
public static readonly DependencyProperty MyPropertyProperty =
    DependencyProperty.Register(
        nameof(MyProperty),                    // 属性名称
        typeof(string),                        // 属性类型
        typeof(MyControl),                     // 所有者类型
        new PropertyMetadata(                  // 元数据
            defaultValue: string.Empty,        // 默认值
            propertyChangedCallback: OnMyPropertyChanged));

public string MyProperty
{
    get => (string)GetValue(MyPropertyProperty);
    set => SetValue(MyPropertyProperty, value);
}

private static void OnMyPropertyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
{
    if (d is MyControl control)
    {
        control.OnMyPropertyChanged((string)e.OldValue, (string)e.NewValue);
    }
}

protected virtual void OnMyPropertyChanged(string oldValue, string newValue)
{
    // 属性变更处理逻辑
}
```

### 3.3 附加属性开发模式

```csharp
public static class GridHelper
{
    public static readonly DependencyProperty ColumnSpanProperty =
        DependencyProperty.RegisterAttached(
            "ColumnSpan",
            typeof(int),
            typeof(GridHelper),
            new PropertyMetadata(1, OnColumnSpanChanged));

    public static int GetColumnSpan(DependencyObject obj)
    {
        return (int)obj.GetValue(ColumnSpanProperty);
    }

    public static void SetColumnSpan(DependencyObject obj, int value)
    {
        obj.SetValue(ColumnSpanProperty, value);
    }

    private static void OnColumnSpanChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FrameworkElement element && element.Parent is Grid grid)
        {
            Grid.SetColumnSpan(element, (int)e.NewValue);
        }
    }
}
```

---

## 4. 样式和主题规范

### 4.1 资源字典文件组织

```
Themes/
├── Styles/
│   ├── ButtonStyles.xaml
│   ├── TextBoxStyles.xaml
│   └── DataGridStyles.xaml
├── Resources/
│   ├── Colors.xaml
│   ├── Fonts.xaml
│   └── Brushes.xaml
└── Themes.xaml                 # 主题合并文件
```

### 4.2 设计令牌系统

**Colors.xaml - 颜色定义：**
```xml
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 主色调 -->
    <Color x:Key="PrimaryColor">#FF007ACC</Color>
    <Color x:Key="PrimaryLightColor">#FF4A9EE7</Color>
    <Color x:Key="PrimaryDarkColor">#FF005A9E</Color>
    
    <!-- 中性色调 -->
    <Color x:Key="BackgroundColor">#FFFFFFFF</Color>
    <Color x:Key="TextPrimaryColor">#FF212529</Color>
    
    <!-- 画刷定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}" />
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}" />

</ResourceDictionary>
```

### 4.3 主题切换机制

```csharp
public class ThemeManager : INotifyPropertyChanged
{
    private static ThemeManager _instance;
    private string _currentTheme = "Light";

    public static ThemeManager Instance => _instance ??= new ThemeManager();

    public string CurrentTheme
    {
        get => _currentTheme;
        set
        {
            if (_currentTheme != value)
            {
                _currentTheme = value;
                OnPropertyChanged();
                ApplyTheme(value);
            }
        }
    }

    public event PropertyChangedEventHandler PropertyChanged;

    private void ApplyTheme(string themeName)
    {
        var app = Application.Current;
        
        // 移除当前主题
        var existingTheme = app.Resources.MergedDictionaries
            .FirstOrDefault(d => d.Source?.OriginalString.Contains("Themes/") == true);
        if (existingTheme != null)
        {
            app.Resources.MergedDictionaries.Remove(existingTheme);
        }

        // 应用新主题
        var themeUri = new Uri($"pack://application:,,,/Themes/{themeName}Theme.xaml");
        var themeDict = new ResourceDictionary { Source = themeUri };
        app.Resources.MergedDictionaries.Add(themeDict);
    }

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
```

---

## 5. 行为(Behaviors)开发规范

### 5.1 Attached Behaviors 实现模板

```csharp
public abstract class AttachedBehavior<T> where T : DependencyObject
{
    public static readonly DependencyProperty IsEnabledProperty =
        DependencyProperty.RegisterAttached(
            "IsEnabled",
            typeof(bool),
            typeof(AttachedBehavior<T>),
            new PropertyMetadata(false, OnIsEnabledChanged));

    public static bool GetIsEnabled(DependencyObject obj)
    {
        return (bool)obj.GetValue(IsEnabledProperty);
    }

    public static void SetIsEnabled(DependencyObject obj, bool value)
    {
        obj.SetValue(IsEnabledProperty, value);
    }

    private static void OnIsEnabledChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is T target)
        {
            if ((bool)e.NewValue)
            {
                OnAttached(target);
            }
            else
            {
                OnDetached(target);
            }
        }
    }

    protected static abstract void OnAttached(T target);
    protected static abstract void OnDetached(T target);
}
```

### 5.2 Microsoft.Xaml.Behaviors.Wpf 使用

```xml
<!-- 在XAML中引用 -->
<Window xmlns:i="http://schemas.microsoft.com/xaml/behaviors">
    <Grid>
        <Button Content="点击我">
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="Click">
                    <i:InvokeCommandAction Command="{Binding ClickCommand}" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
        </Button>
    </Grid>
</Window>
```

---

## 6. MVVM架构规范

### 6.1 ViewModel 基类设计

```csharp
public abstract class ViewModelBase : INotifyPropertyChanged, IDisposable
{
    public event PropertyChangedEventHandler PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    public virtual void Dispose()
    {
        // 清理资源
    }
}
```

### 6.2 RelayCommand 实现

```csharp
public class RelayCommand : ICommand
{
    private readonly Action _execute;
    private readonly Func<bool> _canExecute;

    public RelayCommand(Action execute, Func<bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public event EventHandler CanExecuteChanged
    {
        add => CommandManager.RequerySuggested += value;
        remove => CommandManager.RequerySuggested -= value;
    }

    public bool CanExecute(object parameter)
    {
        return _canExecute?.Invoke() ?? true;
    }

    public void Execute(object parameter)
    {
        _execute();
    }
}
```

### 6.3 数据绑定最佳实践

| 绑定场景 | 推荐模式 | 理由 |
|---------|----------|------|
| 显示只读数据 | OneWay | 性能最佳，单向数据流 |
| 用户输入控件 | TwoWay | 支持双向数据同步 |
| 静态资源 | OneTime | 一次性绑定，性能最优 |

**✅ 推荐的绑定写法：**
```xml
<!-- 明确指定绑定模式 -->
<TextBox Text="{Binding CustomerName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

<!-- 使用转换器 -->
<TextBlock Text="{Binding CreateDate, Converter={StaticResource DateTimeToStringConverter}}" />

<!-- 空值处理 -->
<TextBlock Text="{Binding Description, TargetNullValue='暂无描述'}" />
```

---

## 7. 性能优化规范

### 7.1 UI虚拟化应用

```xml
<!-- 启用虚拟化 -->
<ListView ItemsSource="{Binding LargeDataSet}"
          VirtualizingPanel.IsVirtualizing="True"
          VirtualizingPanel.VirtualizationMode="Recycling"
          ScrollViewer.CanContentScroll="True">
    <ListView.ItemsPanel>
        <ItemsPanelTemplate>
            <VirtualizingStackPanel />
        </ItemsPanelTemplate>
    </ListView.ItemsPanel>
</ListView>
```

### 7.2 内存管理和资源释放

**✅ 正确的资源管理：**
```csharp
public class ResourceAwareViewModel : ViewModelBase, IDisposable
{
    private readonly Timer _timer;
    private readonly CancellationTokenSource _cancellationTokenSource;

    public ResourceAwareViewModel()
    {
        _cancellationTokenSource = new CancellationTokenSource();
        _timer = new Timer(OnTimerTick, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));
    }

    public override void Dispose()
    {
        _timer?.Dispose();
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
        
        // 取消事件订阅
        WeakEventManager<SomeService, EventArgs>.RemoveHandler(
            SomeService.Instance, nameof(SomeService.SomeEvent), OnSomeEvent);
        
        base.Dispose();
    }

    private void OnTimerTick(object state)
    {
        if (_cancellationTokenSource.Token.IsCancellationRequested)
            return;
            
        // 定时器逻辑
    }
}
```

### 7.3 数据绑定性能优化

**✅ 高效的绑定实践：**
```csharp
// 使用ObservableCollection而非List
public ObservableCollection<Customer> Customers { get; set; }

// 实现INotifyPropertyChanged的高效版本
private string _name;
public string Name
{
    get => _name;
    set
    {
        if (_name != value)
        {
            _name = value;
            OnPropertyChanged();
            // 只在真正改变时触发相关属性更新
            OnPropertyChanged(nameof(DisplayName));
        }
    }
}
```

---

## 8. 测试规范

### 8.1 单元测试项目结构

```
MyWpfApplication.Tests/
├── ViewModels/
│   ├── CustomerViewModelTests.cs
│   └── OrderViewModelTests.cs
├── Services/
│   ├── CustomerServiceTests.cs
│   └── DataServiceTests.cs
├── Helpers/
│   ├── TestHelper.cs
│   └── MockFactory.cs
└── TestData/
    └── SampleCustomers.json
```

### 8.2 ViewModel 测试策略

```csharp
[TestClass]
public class CustomerViewModelTests
{
    private Mock<ICustomerService> _mockCustomerService;
    private CustomerViewModel _viewModel;

    [TestInitialize]
    public void Setup()
    {
        _mockCustomerService = new Mock<ICustomerService>();
        _viewModel = new CustomerViewModel(_mockCustomerService.Object);
    }

    [TestMethod]
    public async Task LoadCustomers_ShouldPopulateCustomersCollection()
    {
        // Arrange
        var expectedCustomers = new List<Customer>
        {
            new Customer { Id = 1, Name = "张三" },
            new Customer { Id = 2, Name = "李四" }
        };
        
        _mockCustomerService
            .Setup(s => s.GetCustomersAsync())
            .ReturnsAsync(expectedCustomers);

        // Act
        await _viewModel.LoadCustomersCommand.ExecuteAsync(null);

        // Assert
        Assert.AreEqual(2, _viewModel.Customers.Count);
        Assert.AreEqual("张三", _viewModel.Customers[0].Name);
    }

    [TestMethod]
    public void CustomerName_WhenChanged_ShouldRaisePropertyChanged()
    {
        // Arrange
        var propertyChangedRaised = false;
        _viewModel.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(CustomerViewModel.CustomerName))
                propertyChangedRaised = true;
        };

        // Act
        _viewModel.CustomerName = "新客户";

        // Assert
        Assert.IsTrue(propertyChangedRaised);
        Assert.AreEqual("新客户", _viewModel.CustomerName);
    }
}
```

### 8.3 测试覆盖率要求

**测试覆盖率标准：**
- **最低要求**：70%
- **推荐目标**：80%
- **核心业务逻辑**：90%以上
- **UI层**：50%以上（主要测试交互逻辑）

**运行测试和生成覆盖率报告：**
```powershell
# 运行测试并生成覆盖率报告
dotnet test --collect:"XPlat Code Coverage"

# 生成HTML报告
reportgenerator -reports:"TestResults\*\coverage.cobertura.xml" -targetdir:"CoverageReport" -reporttypes:Html
```

---

## 总结

本WPF开发规范文档涵盖了从项目结构到测试的完整开发流程规范。遵循这些规范可以：

1. **提高代码质量**：统一的编码标准和最佳实践
2. **增强可维护性**：清晰的项目结构和命名规范
3. **优化性能**：内存管理和UI虚拟化指导
4. **确保可测试性**：完整的测试策略和覆盖率要求
5. **促进团队协作**：统一的开发规范和代码风格

> **注意**：本规范应根据项目实际需求进行调整，并定期更新以适应技术发展和团队需求变化。

> **警告**：在实施这些规范时，应循序渐进，避免一次性引入过多变化导致开发效率下降。建议先从核心规范开始，逐步完善整个规范体系。
