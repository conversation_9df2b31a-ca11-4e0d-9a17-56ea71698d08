# WPF开发规范文档

## 目录

1. [项目结构规范](#1-项目结构规范)
2. [代码规范](#2-代码规范)
3. [自定义控件开发规范](#3-自定义控件开发规范)
4. [样式和主题规范](#4-样式和主题规范)
5. [行为(Behaviors)开发规范](#5-行为behaviors开发规范)
6. [MVVM架构规范](#6-mvvm架构规范)
7. [性能优化规范](#7-性能优化规范)

---

## 1. 项目结构规范

### 1.1 标准解决方案目录结构

```
MyWpfApplication/                    # 解决方案根目录
├── MyWpfApplication.sln             # 解决方案文件
├── src/                             # 源代码目录
│   ├── MyWpfApplication/            # 主应用项目
│   │   ├── Views/                   # 视图文件
│   │   ├── ViewModels/              # 视图模型
│   │   ├── Models/                  # 数据模型
│   │   ├── Services/                # 业务服务
│   │   ├── Controls/                # 自定义控件
│   │   ├── Themes/                  # 主题样式
│   │   └── Assets/                  # 资源文件
│   ├── MyWpfApplication.Core/       # 核心类库
│   └── MyWpfApplication.Data/       # 数据访问层
├── tests/                           # 测试项目
└── docs/                            # 文档目录
```

### 1.2 项目类型分类

| 项目类型 | 用途 | 输出类型 | 命名规范 |
|---------|------|----------|----------|
| 主应用项目 | WPF应用程序入口 | WPF Application | `{ProjectName}` |
| 核心类库 | 业务逻辑和核心功能 | Class Library | `{ProjectName}.Core` |
| 数据访问层 | 数据访问和持久化 | Class Library | `{ProjectName}.Data` |
| 单元测试 | 业务逻辑测试 | Test Project | `{ProjectName}.Tests` |

### 1.3 命名约定

**✅ 应该做：**
```csharp
// 类名使用PascalCase
public class CustomerViewModel { }
public class OrderService { }

// 文件夹使用PascalCase
Views/CustomerManagement/
Models/BusinessEntities/

// XAML文件使用PascalCase
CustomerListView.xaml
ProductDetailsWindow.xaml
```

**❌ 不应该做：**
```csharp
// 避免使用下划线或连字符
customer_view_model.cs
order-service.cs

// 避免使用缩写或简写
CustVM.cs
OrdSvc.cs
```

### 1.4 资源文件组织方式

```
Assets/
├── Images/
│   ├── Icons/
│   │   ├── 16x16/
│   │   ├── 24x24/
│   │   └── 32x32/
│   ├── Backgrounds/
│   └── Logos/
├── Fonts/
│   ├── Regular/
│   ├── Bold/
│   └── Light/
└── Localization/
    ├── en-US.resx
    ├── zh-CN.resx
    └── ja-JP.resx
```

### 1.5 配置文件管理规范

**App.config 示例：**
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- 应用程序设置 -->
    <add key="DefaultTheme" value="Light" />
    <add key="AutoSave" value="true" />
  </appSettings>

  <connectionStrings>
    <!-- 数据库连接字符串 -->
    <add name="DefaultConnection"
         connectionString="Data Source=localhost;Initial Catalog=MyApp;Integrated Security=True" />
  </connectionStrings>
</configuration>
```

---

## 2. 代码规范

### 2.1 C# 代码风格标准

**✅ 推荐的代码格式：**
```csharp
namespace MyWpfApplication.ViewModels
{
    /// <summary>
    /// 客户管理视图模型
    /// </summary>
    public class CustomerViewModel : ViewModelBase
    {
        #region Private Fields
        
        private string _customerName;
        private ObservableCollection<Customer> _customers;
        
        #endregion
        
        #region Public Properties
        
        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }
        
        #endregion
        
        #region Commands
        
        public ICommand SaveCommand { get; }
        
        #endregion
        
        #region Constructor
        
        public CustomerViewModel()
        {
            SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave);
        }
        
        #endregion
        
        #region Private Methods
        
        private void ExecuteSave()
        {
            // 保存逻辑
        }
        
        private bool CanExecuteSave()
        {
            return !string.IsNullOrWhiteSpace(CustomerName);
        }
        
        #endregion
    }
}
```

### 2.2 命名规范

| 元素类型 | 命名规范 | 示例 |
|---------|----------|------|
| 类 | PascalCase | `CustomerService`, `OrderViewModel` |
| 接口 | I + PascalCase | `ICustomerService`, `IRepository<T>` |
| 方法 | PascalCase | `GetCustomers()`, `SaveOrder()` |
| 属性 | PascalCase | `CustomerName`, `IsEnabled` |
| 字段(private) | _camelCase | `_customerName`, `_isLoading` |
| 常量 | PascalCase | `MaxRetryCount`, `DefaultTimeout` |

### 2.3 XAML 代码格式化规则

**✅ 推荐的XAML格式：**
```xml
<UserControl x:Class="MyWpfApplication.Views.CustomerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:MyWpfApplication.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800">

    <UserControl.DataContext>
        <vm:CustomerViewModel />
    </UserControl.DataContext>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <TextBlock Grid.Row="0"
                   Text="客户管理"
                   FontSize="24"
                   FontWeight="Bold"
                   Margin="0,0,0,20" />

        <!-- 内容区域 -->
        <DataGrid Grid.Row="1"
                  ItemsSource="{Binding Customers}"
                  SelectedItem="{Binding SelectedCustomer}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="姓名"
                                    Binding="{Binding Name}"
                                    Width="200" />
                <DataGridTextColumn Header="邮箱"
                                    Binding="{Binding Email}"
                                    Width="250" />
            </DataGrid.Columns>
        </DataGrid>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,20,0,0">
            <Button Content="保存"
                    Command="{Binding SaveCommand}"
                    Width="80"
                    Height="30"
                    Margin="0,0,10,0" />
            <Button Content="删除"
                    Command="{Binding DeleteCommand}"
                    Width="80"
                    Height="30" />
        </StackPanel>
    </Grid>
</UserControl>
```

### 2.4 XML文档注释标准

```csharp
/// <summary>
/// 表示客户信息的数据模型
/// </summary>
public class Customer
{
    /// <summary>
    /// 获取或设置客户的唯一标识符
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 获取或设置客户姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 根据指定条件搜索客户
    /// </summary>
    /// <param name="criteria">搜索条件</param>
    /// <param name="maxResults">最大返回结果数</param>
    /// <returns>匹配条件的客户列表</returns>
    public List<Customer> Search(string criteria, int maxResults = 100)
    {
        return new List<Customer>();
    }
}
```

---

## 3. 自定义控件开发规范

### 3.1 UserControl vs CustomControl 选择

| 场景 | 推荐类型 | 理由 |
|------|----------|------|
| 组合现有控件 | UserControl | 简单快速，适合业务特定UI |
| 需要完全自定义外观 | CustomControl | 支持主题，可重用性强 |
| 简单的数据展示 | UserControl | 开发成本低 |
| 复杂交互逻辑 | CustomControl | 更好的封装和扩展性 |

**UserControl 示例：**
```csharp
public partial class CustomerCard : UserControl
{
    public static readonly DependencyProperty CustomerProperty =
        DependencyProperty.Register(nameof(Customer), typeof(Customer),
            typeof(CustomerCard), new PropertyMetadata(null, OnCustomerChanged));

    public Customer Customer
    {
        get => (Customer)GetValue(CustomerProperty);
        set => SetValue(CustomerProperty, value);
    }

    public CustomerCard() => InitializeComponent();

    private static void OnCustomerChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is CustomerCard control) control.UpdateDisplay();
    }

    private void UpdateDisplay() { /* 更新UI显示 */ }
}
```

**CustomControl 示例：**
```csharp
[TemplatePart(Name = PART_ContentPresenter, Type = typeof(ContentPresenter))]
public class CustomButton : ButtonBase
{
    private const string PART_ContentPresenter = "PART_ContentPresenter";

    public static readonly DependencyProperty CornerRadiusProperty =
        DependencyProperty.Register(nameof(CornerRadius), typeof(CornerRadius),
            typeof(CustomButton), new PropertyMetadata(new CornerRadius(0)));

    public CornerRadius CornerRadius
    {
        get => (CornerRadius)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    static CustomButton()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(CustomButton),
            new FrameworkPropertyMetadata(typeof(CustomButton)));
    }

    public override void OnApplyTemplate()
    {
        base.OnApplyTemplate();
        UpdateVisualState(false);
    }

    private void UpdateVisualState(bool useTransitions)
    {
        var state = IsMouseOver ? "MouseOver" : "Normal";
        VisualStateManager.GoToState(this, state, useTransitions);
    }
}
```

### 3.2 依赖属性定义模板

**标准依赖属性模板：**
```csharp
public static readonly DependencyProperty MyPropertyProperty =
    DependencyProperty.Register(
        nameof(MyProperty),                    // 属性名称
        typeof(string),                        // 属性类型
        typeof(MyControl),                     // 所有者类型
        new PropertyMetadata(                  // 元数据
            defaultValue: string.Empty,        // 默认值
            propertyChangedCallback: OnMyPropertyChanged,  // 变更回调
            coerceValueCallback: CoerceMyProperty));       // 值强制回调

public string MyProperty
{
    get => (string)GetValue(MyPropertyProperty);
    set => SetValue(MyPropertyProperty, value);
}

private static void OnMyPropertyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
{
    if (d is MyControl control)
    {
        control.OnMyPropertyChanged((string)e.OldValue, (string)e.NewValue);
    }
}

private static object CoerceMyProperty(DependencyObject d, object value)
{
    // 值验证和强制逻辑
    if (value is string stringValue && stringValue.Length > 100)
    {
        return stringValue.Substring(0, 100);
    }
    return value;
}

protected virtual void OnMyPropertyChanged(string oldValue, string newValue)
{
    // 属性变更处理逻辑
}
```

### 3.3 附加属性开发模式

```csharp
public static class GridHelper
{
    public static readonly DependencyProperty ColumnSpanProperty =
        DependencyProperty.RegisterAttached(
            "ColumnSpan",
            typeof(int),
            typeof(GridHelper),
            new PropertyMetadata(1, OnColumnSpanChanged));

    public static int GetColumnSpan(DependencyObject obj)
    {
        return (int)obj.GetValue(ColumnSpanProperty);
    }

    public static void SetColumnSpan(DependencyObject obj, int value)
    {
        obj.SetValue(ColumnSpanProperty, value);
    }

    private static void OnColumnSpanChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FrameworkElement element && element.Parent is Grid grid)
        {
            Grid.SetColumnSpan(element, (int)e.NewValue);
        }
    }
}
```

**使用附加属性：**
```xml
<Grid>
    <TextBlock Text="跨列文本"
               local:GridHelper.ColumnSpan="2" />
</Grid>
```

### 3.4 控件模板设计原则

**Generic.xaml 中的控件模板：**
```xml
<Style TargetType="{x:Type local:CustomButton}">
    <Setter Property="Background" Value="#FF007ACC" />
    <Setter Property="Foreground" Value="White" />
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="{x:Type local:CustomButton}">
                <Border x:Name="PART_Border"
                        Background="{TemplateBinding Background}"
                        CornerRadius="{TemplateBinding CornerRadius}">

                    <VisualStateManager.VisualStateGroups>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Normal" />
                            <VisualState x:Name="MouseOver">
                                <Storyboard>
                                    <ColorAnimation Storyboard.TargetName="PART_Border"
                                                    Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                    To="#FF005A9E" Duration="0:0:0.1" />
                                </Storyboard>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateManager.VisualStateGroups>

                    <ContentPresenter Content="{TemplateBinding Content}" />
                </Border>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

---

## 4. 样式和主题规范

### 4.1 资源字典文件组织

```
Themes/
├── Styles/
│   ├── ButtonStyles.xaml
│   ├── TextBoxStyles.xaml
│   └── DataGridStyles.xaml
├── Resources/
│   ├── Colors.xaml
│   ├── Fonts.xaml
│   └── Brushes.xaml
└── Themes.xaml                 # 主题合并文件
```

### 4.2 设计令牌系统

**Colors.xaml - 颜色定义：**
```xml
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 主色调 -->
    <Color x:Key="PrimaryColor">#FF007ACC</Color>
    <Color x:Key="PrimaryLightColor">#FF4A9EE7</Color>
    <Color x:Key="PrimaryDarkColor">#FF005A9E</Color>

    <!-- 辅助色调 -->
    <Color x:Key="SecondaryColor">#FF6C757D</Color>
    <Color x:Key="AccentColor">#FF28A745</Color>
    <Color x:Key="WarningColor">#FFFFC107</Color>
    <Color x:Key="ErrorColor">#FFDC3545</Color>

    <!-- 中性色调 -->
    <Color x:Key="BackgroundColor">#FFFFFFFF</Color>
    <Color x:Key="SurfaceColor">#FFF8F9FA</Color>
    <Color x:Key="BorderColor">#FFDEE2E6</Color>
    <Color x:Key="TextPrimaryColor">#FF212529</Color>
    <Color x:Key="TextSecondaryColor">#FF6C757D</Color>

    <!-- 画刷定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}" />
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}" />
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}" />

</ResourceDictionary>
```

**Fonts.xaml - 字体定义：**
```xml
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- 字体族 -->
    <FontFamily x:Key="PrimaryFontFamily">Microsoft YaHei UI, Segoe UI, Arial</FontFamily>

    <!-- 字体大小 -->
    <sys:Double x:Key="FontSizeSmall">12</sys:Double>
    <sys:Double x:Key="FontSizeNormal">14</sys:Double>
    <sys:Double x:Key="FontSizeLarge">16</sys:Double>
    <sys:Double x:Key="FontSizeXLarge">20</sys:Double>

</ResourceDictionary>
```

### 4.3 主题切换机制实现

**主题管理器：**
```csharp
public class ThemeManager : INotifyPropertyChanged
{
    private static ThemeManager _instance;
    private string _currentTheme = "Light";

    public static ThemeManager Instance => _instance ??= new ThemeManager();

    public string CurrentTheme
    {
        get => _currentTheme;
        set
        {
            if (_currentTheme != value)
            {
                _currentTheme = value;
                OnPropertyChanged();
                ApplyTheme(value);
            }
        }
    }

    public event PropertyChangedEventHandler PropertyChanged;

    private void ApplyTheme(string themeName)
    {
        var app = Application.Current;

        // 移除当前主题
        var existingTheme = app.Resources.MergedDictionaries
            .FirstOrDefault(d => d.Source?.OriginalString.Contains("Themes/") == true);
        if (existingTheme != null)
        {
            app.Resources.MergedDictionaries.Remove(existingTheme);
        }

        // 应用新主题
        var themeUri = new Uri($"pack://application:,,,/Themes/{themeName}Theme.xaml");
        var themeDict = new ResourceDictionary { Source = themeUri };
        app.Resources.MergedDictionaries.Add(themeDict);
    }

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
```

**主题切换控件：**
```xml
<ComboBox ItemsSource="{Binding AvailableThemes}"
          SelectedItem="{Binding Source={x:Static local:ThemeManager.Instance},
                                 Path=CurrentTheme}"
          Width="120">
    <ComboBox.ItemTemplate>
        <DataTemplate>
            <TextBlock Text="{Binding}" />
        </DataTemplate>
    </ComboBox.ItemTemplate>
</ComboBox>
```

### 4.4 响应式设计规范

**响应式布局示例：**
```xml
<Grid>
    <VisualStateManager.VisualStateGroups>
        <VisualStateGroup x:Name="WindowStates">
            <VisualState x:Name="WideState">
                <VisualState.StateTriggers>
                    <AdaptiveTrigger MinWindowWidth="800" />
                </VisualState.StateTriggers>
                <VisualState.Setters>
                    <Setter Target="ContentPanel.Orientation" Value="Horizontal" />
                </VisualState.Setters>
            </VisualState>
            <VisualState x:Name="NarrowState">
                <VisualState.StateTriggers>
                    <AdaptiveTrigger MinWindowWidth="0" />
                </VisualState.StateTriggers>
                <VisualState.Setters>
                    <Setter Target="ContentPanel.Orientation" Value="Vertical" />
                </VisualState.Setters>
            </VisualState>
        </VisualStateGroup>
    </VisualStateManager.VisualStateGroups>

    <StackPanel x:Name="ContentPanel">
        <!-- 内容区域 -->
    </StackPanel>
</Grid>
```

---

## 5. 行为(Behaviors)开发规范

### 5.1 Attached Behaviors 实现模板

**基础行为基类：**
```csharp
public abstract class AttachedBehavior<T> where T : DependencyObject
{
    public static readonly DependencyProperty IsEnabledProperty =
        DependencyProperty.RegisterAttached(
            "IsEnabled",
            typeof(bool),
            typeof(AttachedBehavior<T>),
            new PropertyMetadata(false, OnIsEnabledChanged));

    public static bool GetIsEnabled(DependencyObject obj)
    {
        return (bool)obj.GetValue(IsEnabledProperty);
    }

    public static void SetIsEnabled(DependencyObject obj, bool value)
    {
        obj.SetValue(IsEnabledProperty, value);
    }

    private static void OnIsEnabledChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is T target)
        {
            if ((bool)e.NewValue)
            {
                OnAttached(target);
            }
            else
            {
                OnDetached(target);
            }
        }
    }

    protected static abstract void OnAttached(T target);
    protected static abstract void OnDetached(T target);
}
```

**具体行为实现示例：**
```csharp
public class TextBoxWatermarkBehavior : AttachedBehavior<TextBox>
{
    public static readonly DependencyProperty WatermarkProperty =
        DependencyProperty.RegisterAttached("Watermark", typeof(string),
            typeof(TextBoxWatermarkBehavior), new PropertyMetadata(string.Empty));

    public static string GetWatermark(DependencyObject obj) => (string)obj.GetValue(WatermarkProperty);
    public static void SetWatermark(DependencyObject obj, string value) => obj.SetValue(WatermarkProperty, value);

    protected static override void OnAttached(TextBox target)
    {
        target.GotFocus += OnGotFocus;
        target.LostFocus += OnLostFocus;
        UpdateWatermark(target);
    }

    protected static override void OnDetached(TextBox target)
    {
        target.GotFocus -= OnGotFocus;
        target.LostFocus -= OnLostFocus;
    }

    private static void OnGotFocus(object sender, RoutedEventArgs e) => UpdateWatermark((TextBox)sender);
    private static void OnLostFocus(object sender, RoutedEventArgs e) => UpdateWatermark((TextBox)sender);

    private static void UpdateWatermark(TextBox textBox)
    {
        var watermark = GetWatermark(textBox);
        textBox.Background = string.IsNullOrEmpty(textBox.Text) && !textBox.IsFocused
            ? new SolidColorBrush(Colors.LightGray)
            : Brushes.White;
    }
}
```

**使用行为：**
```xml
<TextBox local:TextBoxWatermarkBehavior.IsEnabled="True"
         local:TextBoxWatermarkBehavior.Watermark="请输入用户名..." />
```

### 5.2 Microsoft.Xaml.Behaviors.Wpf 使用规范

**安装和引用：**
```xml
<!-- 在XAML中引用 -->
<Window xmlns:i="http://schemas.microsoft.com/xaml/behaviors">
    <Grid>
        <Button Content="点击我">
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="Click">
                    <i:InvokeCommandAction Command="{Binding ClickCommand}" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
        </Button>
    </Grid>
</Window>
```

**自定义行为继承：**
```csharp
public class FadeInBehavior : Behavior<FrameworkElement>
{
    public static readonly DependencyProperty DurationProperty =
        DependencyProperty.Register(nameof(Duration), typeof(TimeSpan),
            typeof(FadeInBehavior), new PropertyMetadata(TimeSpan.FromSeconds(1)));

    public TimeSpan Duration
    {
        get => (TimeSpan)GetValue(DurationProperty);
        set => SetValue(DurationProperty, value);
    }

    protected override void OnAttached()
    {
        base.OnAttached();
        AssociatedObject.Loaded += OnLoaded;
    }

    protected override void OnDetaching()
    {
        base.OnDetaching();
        AssociatedObject.Loaded -= OnLoaded;
    }

    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        var fadeIn = new DoubleAnimation(0, 1, Duration);
        AssociatedObject.BeginAnimation(UIElement.OpacityProperty, fadeIn);
    }
}
```

---

## 6. MVVM架构规范

### 6.1 ViewModel 基类设计

```csharp
public abstract class ViewModelBase : INotifyPropertyChanged, IDisposable
{
    public event PropertyChangedEventHandler PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    public virtual void Dispose()
    {
        // 清理资源
    }
}
```

### 6.2 RelayCommand 实现

**RelayCommand 标准实现：**
```csharp
public class RelayCommand : ICommand
{
    private readonly Action _execute;
    private readonly Func<bool> _canExecute;

    public RelayCommand(Action execute, Func<bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public event EventHandler CanExecuteChanged
    {
        add => CommandManager.RequerySuggested += value;
        remove => CommandManager.RequerySuggested -= value;
    }

    public bool CanExecute(object parameter) => _canExecute?.Invoke() ?? true;
    public void Execute(object parameter) => _execute();
}

public class RelayCommand<T> : ICommand
{
    private readonly Action<T> _execute;
    private readonly Func<T, bool> _canExecute;

    public RelayCommand(Action<T> execute, Func<T, bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public event EventHandler CanExecuteChanged
    {
        add => CommandManager.RequerySuggested += value;
        remove => CommandManager.RequerySuggested -= value;
    }

    public bool CanExecute(object parameter) => _canExecute?.Invoke((T)parameter) ?? true;
    public void Execute(object parameter) => _execute((T)parameter);
}
```

### 6.3 数据绑定最佳实践

**绑定模式选择指南：**

| 绑定场景 | 推荐模式 | 理由 |
|---------|----------|------|
| 显示只读数据 | OneWay | 性能最佳，单向数据流 |
| 用户输入控件 | TwoWay | 支持双向数据同步 |
| 静态资源 | OneTime | 一次性绑定，性能最优 |
| 列表显示 | OneWay | 避免不必要的反向更新 |

**✅ 推荐的绑定写法：**
```xml
<!-- 明确指定绑定模式 -->
<TextBox Text="{Binding CustomerName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

<!-- 使用转换器 -->
<TextBlock Text="{Binding CreateDate, Converter={StaticResource DateTimeToStringConverter}}" />

<!-- 绑定验证 -->
<TextBox Text="{Binding Email, Mode=TwoWay, ValidatesOnDataErrors=True, NotifyOnValidationError=True}" />

<!-- 空值处理 -->
<TextBlock Text="{Binding Description, TargetNullValue='暂无描述'}" />
```

**❌ 避免的绑定写法：**
```xml
<!-- 避免不必要的TwoWay绑定 -->
<TextBlock Text="{Binding CustomerName, Mode=TwoWay}" />

<!-- 避免在模板中使用复杂的多级绑定 -->
<TextBlock Text="{Binding Customer.Address.City.Name}" />
```

### 6.4 视图和视图模型解耦策略

**使用依赖注入：**
```csharp
public interface ICustomerService
{
    Task<List<Customer>> GetCustomersAsync();
    Task SaveCustomerAsync(Customer customer);
}

public class CustomerViewModel : ViewModelBase
{
    private readonly ICustomerService _customerService;
    private ObservableCollection<Customer> _customers;

    public CustomerViewModel(ICustomerService customerService)
    {
        _customerService = customerService;
        LoadCustomersCommand = new RelayCommand(async () => await LoadCustomers());
    }

    public ObservableCollection<Customer> Customers
    {
        get => _customers;
        set => SetProperty(ref _customers, value);
    }

    public ICommand LoadCustomersCommand { get; }

    private async Task LoadCustomers()
    {
        var customers = await _customerService.GetCustomersAsync();
        Customers = new ObservableCollection<Customer>(customers);
    }
}
```

**服务定位器模式：**
```csharp
public class ServiceLocator
{
    private static readonly Dictionary<Type, object> _services = new();

    public static void RegisterService<T>(T service) => _services[typeof(T)] = service;
    public static T GetService<T>() => (T)_services[typeof(T)];
}

// 在App.xaml.cs中注册服务
protected override void OnStartup(StartupEventArgs e)
{
    base.OnStartup(e);
    ServiceLocator.RegisterService<ICustomerService>(new CustomerService());
}
```

---

## 7. 性能优化规范

### 7.1 UI虚拟化应用

**ListView/DataGrid 虚拟化：**
```xml
<!-- 启用虚拟化 -->
<ListView ItemsSource="{Binding LargeDataSet}"
          VirtualizingPanel.IsVirtualizing="True"
          VirtualizingPanel.VirtualizationMode="Recycling"
          VirtualizingPanel.IsContainerVirtualizable="True"
          ScrollViewer.CanContentScroll="True">
    <ListView.ItemsPanel>
        <ItemsPanelTemplate>
            <VirtualizingStackPanel />
        </ItemsPanelTemplate>
    </ListView.ItemsPanel>
</ListView>
```

**虚拟化最佳实践：**
```xml
<!-- 启用容器回收以提高性能 -->
<ListView VirtualizingPanel.VirtualizationMode="Recycling"
          VirtualizingPanel.IsVirtualizing="True"
          VirtualizingPanel.IsContainerVirtualizable="True" />
```

```csharp
// 对于大数据集，使用异步加载
public async Task LoadDataAsync()
{
    var data = await DataService.GetLargeDataSetAsync();
    Application.Current.Dispatcher.Invoke(() =>
    {
        Items.Clear();
        foreach (var item in data) Items.Add(item);
    });
}
```

### 7.2 内存管理和资源释放

**✅ 正确的资源管理：**
```csharp
public class ResourceAwareViewModel : ViewModelBase, IDisposable
{
    private readonly Timer _timer;
    private readonly CancellationTokenSource _cancellationTokenSource;

    public ResourceAwareViewModel()
    {
        _cancellationTokenSource = new CancellationTokenSource();
        _timer = new Timer(OnTimerTick, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));
    }

    public override void Dispose()
    {
        _timer?.Dispose();
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();

        // 取消事件订阅
        WeakEventManager<SomeService, EventArgs>.RemoveHandler(
            SomeService.Instance, nameof(SomeService.SomeEvent), OnSomeEvent);

        base.Dispose();
    }

    private void OnTimerTick(object state)
    {
        if (_cancellationTokenSource.Token.IsCancellationRequested) return;
        // 定时器逻辑
    }
}
```

### 7.3 数据绑定性能优化

**✅ 高效的绑定实践：**
```csharp
// 使用ObservableCollection而非List
public ObservableCollection<Customer> Customers { get; set; }

// 批量更新时暂停通知
public void UpdateCustomers(List<Customer> newCustomers)
{
    using (Customers.DeferRefresh())
    {
        Customers.Clear();
        foreach (var customer in newCustomers) Customers.Add(customer);
    }
}

// 实现INotifyPropertyChanged的高效版本
private string _name;
public string Name
{
    get => _name;
    set
    {
        if (_name != value)
        {
            _name = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(DisplayName)); // 触发相关属性更新
        }
    }
}
```

**❌ 避免的性能陷阱：**
```csharp
// ❌ 避免在属性getter中进行复杂计算
public string ExpensiveProperty => SomeExpensiveCalculation();

// ✅ 应该缓存计算结果
private string _cachedProperty;
private bool _isDirty = true;

public string ExpensiveProperty
{
    get
    {
        if (_isDirty)
        {
            _cachedProperty = SomeExpensiveCalculation();
            _isDirty = false;
        }
        return _cachedProperty;
    }
}
```



---

## 总结

本WPF开发规范文档涵盖了从项目结构到性能优化的完整开发流程规范。遵循这些规范可以：

### 🎯 核心价值

1. **提高代码质量**：统一的编码标准和最佳实践
2. **增强可维护性**：清晰的项目结构和命名规范
3. **优化性能**：内存管理和UI虚拟化指导
4. **促进团队协作**：统一的开发规范和代码风格
5. **加速开发效率**：标准化的模板和工具链

### 📋 实施建议

**分阶段实施：**
1. **基础规范**：项目结构、代码风格、命名规范
2. **架构规范**：MVVM模式、依赖注入、自定义控件
3. **高级优化**：性能优化、主题系统、行为模式

### ⚠️ 注意事项

> **重要提醒**：本规范应根据项目实际需求进行调整，并定期更新以适应技术发展和团队需求变化。

> **实施警告**：在实施这些规范时，应循序渐进，避免一次性引入过多变化导致开发效率下降。建议先从核心规范开始，逐步完善整个规范体系。
